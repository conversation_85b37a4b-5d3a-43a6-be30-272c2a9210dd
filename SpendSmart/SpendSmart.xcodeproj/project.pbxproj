// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		5060B9062D827079000CCB51 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 5060B9052D827079000CCB51 /* Supabase */; };
		50A4FA3C2D909F7200C89441 /* GoogleGenerativeAI in Frameworks */ = {isa = PBXBuildFile; productRef = 50A4FA3B2D909F7200C89441 /* GoogleGenerativeAI */; };
		50B538DF2DAC2DBC00A2999A /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 50B538DE2DAC2DBC00A2999A /* Auth */; };
		50B538E12DAC2DBC00A2999A /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 50B538E02DAC2DBC00A2999A /* Functions */; };
		50B538E32DAC2DBC00A2999A /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 50B538E22DAC2DBC00A2999A /* PostgREST */; };
		50B538E52DAC2DBC00A2999A /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 50B538E42DAC2DBC00A2999A /* Realtime */; };
		50B538E72DAC2DBC00A2999A /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 50B538E62DAC2DBC00A2999A /* Storage */; };
		50E064712DEE4183009E889E /* OpenAI in Frameworks */ = {isa = PBXBuildFile; productRef = 50E064702DEE4183009E889E /* OpenAI */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		507C569A2D8254DC00CA1FD5 /* SpendSmart.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SpendSmart.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		50A8B0032D84B68E00772840 /* Exceptions for "SpendSmart" folder in "SpendSmart" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 507C56992D8254DC00CA1FD5 /* SpendSmart */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		507C569C2D8254DC00CA1FD5 /* SpendSmart */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				50A8B0032D84B68E00772840 /* Exceptions for "SpendSmart" folder in "SpendSmart" target */,
			);
			path = SpendSmart;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		507C56972D8254DC00CA1FD5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5060B9062D827079000CCB51 /* Supabase in Frameworks */,
				50A4FA3C2D909F7200C89441 /* GoogleGenerativeAI in Frameworks */,
				50E064712DEE4183009E889E /* OpenAI in Frameworks */,
				50B538E72DAC2DBC00A2999A /* Storage in Frameworks */,
				50B538E12DAC2DBC00A2999A /* Functions in Frameworks */,
				50B538E52DAC2DBC00A2999A /* Realtime in Frameworks */,
				50B538DF2DAC2DBC00A2999A /* Auth in Frameworks */,
				50B538E32DAC2DBC00A2999A /* PostgREST in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		507C56912D8254DC00CA1FD5 = {
			isa = PBXGroup;
			children = (
				507C569C2D8254DC00CA1FD5 /* SpendSmart */,
				507C569B2D8254DC00CA1FD5 /* Products */,
			);
			sourceTree = "<group>";
		};
		507C569B2D8254DC00CA1FD5 /* Products */ = {
			isa = PBXGroup;
			children = (
				507C569A2D8254DC00CA1FD5 /* SpendSmart.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		507C56992D8254DC00CA1FD5 /* SpendSmart */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 507C56A82D8254DE00CA1FD5 /* Build configuration list for PBXNativeTarget "SpendSmart" */;
			buildPhases = (
				507C56962D8254DC00CA1FD5 /* Sources */,
				507C56972D8254DC00CA1FD5 /* Frameworks */,
				507C56982D8254DC00CA1FD5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				507C569C2D8254DC00CA1FD5 /* SpendSmart */,
			);
			name = SpendSmart;
			packageProductDependencies = (
				5060B9052D827079000CCB51 /* Supabase */,
				50A4FA3B2D909F7200C89441 /* GoogleGenerativeAI */,
				50B538DE2DAC2DBC00A2999A /* Auth */,
				50B538E02DAC2DBC00A2999A /* Functions */,
				50B538E22DAC2DBC00A2999A /* PostgREST */,
				50B538E42DAC2DBC00A2999A /* Realtime */,
				50B538E62DAC2DBC00A2999A /* Storage */,
				50E064702DEE4183009E889E /* OpenAI */,
			);
			productName = SpendSmart;
			productReference = 507C569A2D8254DC00CA1FD5 /* SpendSmart.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		507C56922D8254DC00CA1FD5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					507C56992D8254DC00CA1FD5 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 507C56952D8254DC00CA1FD5 /* Build configuration list for PBXProject "SpendSmart" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 507C56912D8254DC00CA1FD5;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */,
				50A4FA3A2D909F7200C89441 /* XCRemoteSwiftPackageReference "generative-ai-swift" */,
				50E0646F2DEE4183009E889E /* XCRemoteSwiftPackageReference "OpenAI" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 507C569B2D8254DC00CA1FD5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				507C56992D8254DC00CA1FD5 /* SpendSmart */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		507C56982D8254DC00CA1FD5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		507C56962D8254DC00CA1FD5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		507C56A62D8254DE00CA1FD5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = P6PV2R9443;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		507C56A72D8254DE00CA1FD5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = P6PV2R9443;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		507C56A92D8254DE00CA1FD5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SpendSmart/SpendSmart.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4;
				DEVELOPMENT_ASSET_PATHS = "\"SpendSmart/Preview Content\"";
				DEVELOPMENT_TEAM = M542AQBJC5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SpendSmart/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SpendSmart;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.shopping";
				INFOPLIST_KEY_NSCameraUsageDescription = "SpendSmart needs camera access to scan your receipts and automatically extract information.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "SpendSmart needs access to your photo library to select receipt images for processing.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shauryag.SpendSmartAppStore;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		507C56AA2D8254DE00CA1FD5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SpendSmart/SpendSmart.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4;
				DEVELOPMENT_ASSET_PATHS = "\"SpendSmart/Preview Content\"";
				DEVELOPMENT_TEAM = M542AQBJC5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SpendSmart/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SpendSmart;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.shopping";
				INFOPLIST_KEY_NSCameraUsageDescription = "SpendSmart needs camera access to scan your receipts and automatically extract information.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "SpendSmart needs access to your photo library to select receipt images for processing.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shauryag.SpendSmartAppStore;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		507C56952D8254DC00CA1FD5 /* Build configuration list for PBXProject "SpendSmart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				507C56A62D8254DE00CA1FD5 /* Debug */,
				507C56A72D8254DE00CA1FD5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		507C56A82D8254DE00CA1FD5 /* Build configuration list for PBXNativeTarget "SpendSmart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				507C56A92D8254DE00CA1FD5 /* Debug */,
				507C56AA2D8254DE00CA1FD5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
		50A4FA3A2D909F7200C89441 /* XCRemoteSwiftPackageReference "generative-ai-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google-gemini/generative-ai-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.5.6;
			};
		};
		50E0646F2DEE4183009E889E /* XCRemoteSwiftPackageReference "OpenAI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/MacPaw/OpenAI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.4.3;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		5060B9052D827079000CCB51 /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
		50A4FA3B2D909F7200C89441 /* GoogleGenerativeAI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 50A4FA3A2D909F7200C89441 /* XCRemoteSwiftPackageReference "generative-ai-swift" */;
			productName = GoogleGenerativeAI;
		};
		50B538DE2DAC2DBC00A2999A /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		50B538E02DAC2DBC00A2999A /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		50B538E22DAC2DBC00A2999A /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		50B538E42DAC2DBC00A2999A /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		50B538E62DAC2DBC00A2999A /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5060B9042D827079000CCB51 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		50E064702DEE4183009E889E /* OpenAI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 50E0646F2DEE4183009E889E /* XCRemoteSwiftPackageReference "OpenAI" */;
			productName = OpenAI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 507C56922D8254DC00CA1FD5 /* Project object */;
}
