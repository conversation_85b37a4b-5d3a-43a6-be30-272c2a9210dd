const { createClient } = require('@supabase/supabase-js');

// Create Supabase client with anon key (for regular operations)
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
);

// Create Supabase client with service role key (for admin operations)
const supabaseAdmin = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

module.exports = {
    supabase,
    supabaseAdmin
};
