# Environment-Based Backend Configuration

This document explains how the SpendSmart app handles backend URL detection and local network permissions based on the build environment.

## 🏗️ Architecture

The app uses different backend URLs based on the build configuration:

### Development (DEBUG builds)
- **Primary**: Tests localhost first (`http://*************:3000`)
- **Fallback**: Production URL if localhost unavailable
- **Network Permission**: Shows local network permission popup
- **Use Case**: When developing the app locally

### Production (RELEASE builds)
- **Primary**: Always uses production URL (Vercel)
- **Fallback**: None (always production)
- **Network Permission**: No local network permission popup
- **Use Case**: App Store builds and user installations

## 🔧 How It Works

### Automatic Detection
The app automatically detects the environment using Swift's `#if DEBUG` compiler directive:

```swift
#if DEBUG
    // Development mode
    _isDevelopment = true
#else
    // Production mode
    _isDevelopment = false
#endif
```

### Backend URL Selection
1. **Development**: Tests localhost connectivity → Falls back to production
2. **Production**: Always uses production URL (no localhost testing)

### Network Permissions
- **Development**: Requests local network access (shows permission popup)
- **Production**: No local network access needed (no popup)

## 📱 User Experience

### Development
- User sees local network permission popup
- App tests localhost first
- Falls back to production if localhost unavailable
- Perfect for development and testing

### Production
- No permission popup
- Direct connection to Vercel backend
- Faster app startup (no connectivity testing)
- Better user experience

## 🚀 Deployment Workflow

### 1. Deploy Backend to Vercel
```bash
cd SpendSmart-Backend
vercel --prod
```

### 2. Update Production URL
When you get the Vercel URL, update it in the app:

```swift
// In your app (temporarily for testing)
ProductionURLUpdater.updateProductionURL("https://your-vercel-app.vercel.app")
```

### 3. Test Production URL
```swift
let isReachable = await ProductionURLUpdater.testProductionURL()
print("Production URL reachable: \(isReachable)")
```

### 4. Build for Production
- Use Xcode's "Release" configuration
- Archive and upload to App Store
- Users get production behavior (no local network popup)

## 🔍 Debugging

### Check Current Environment
```swift
print("Environment: \(BackendConfig.shared.isDevelopment ? "Development" : "Production")")
```

### Check Current Backend URL
```swift
let url = await BackendConfig.shared.activeBackendURL
print("Active backend: \(url)")
```

### Force Refresh Detection
```swift
let newURL = await BackendConfig.shared.refreshBackendDetection()
print("New backend URL: \(newURL)")
```

## 📋 Configuration Files

### Info.plist
- Contains network security exceptions for both development and production
- Local network usage description for development
- TLS configuration for production domains

### APIKeys.swift
- Contains the BackendConfig class
- Handles environment detection
- Manages URL selection logic

## 🎯 Benefits

1. **No Permission Popup in Production**: Users don't see unnecessary permission requests
2. **Automatic Environment Detection**: No manual configuration needed
3. **Development Flexibility**: Easy local development with localhost
4. **Production Reliability**: Always uses production backend
5. **Better User Experience**: Faster startup in production

## 🔄 Migration from Old System

The old system always tested localhost, which caused permission popups in production. The new system:

1. **Detects build environment** automatically
2. **Only tests localhost in development**
3. **Always uses production URL in production**
4. **Eliminates unnecessary permission requests**

This provides a much better user experience while maintaining development flexibility. 