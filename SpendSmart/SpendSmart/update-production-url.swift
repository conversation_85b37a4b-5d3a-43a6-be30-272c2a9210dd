//
//  update-production-url.swift
//  SpendSmart
//
//  Helper script to update the production URL
//  Run this script when you deploy to Vercel and get the new URL
//

import Foundation

// MARK: - Production URL Update Helper

/// Helper class to update the production URL
class ProductionURLUpdater {
    
    /// Update the production URL in the BackendConfig
    /// Call this when you deploy to Vercel and get the new URL
    /// 
    /// Example usage:
    /// ```
    /// ProductionURLUpdater.updateProductionURL("https://spendsmart-backend.vercel.app")
    /// ```
    static func updateProductionURL(_ newURL: String) {
        print("🔧 [iOS] Updating production URL to: \(newURL)")
        
        // Update the BackendConfig
        BackendConfig.shared.updateProductionURL(newURL)
        
        print("✅ [iOS] Production URL updated successfully!")
        print("📝 [iOS] The app will now use this URL in production builds")
        print("📝 [iOS] Development builds will still test localhost first")
    }
    
    /// Get the current production URL
    static func getCurrentProductionURL() -> String {
        return BackendConfig.shared.currentProductionURL
    }
    
    /// Test the production URL connectivity
    static func testProductionURL() async -> Bool {
        let url = BackendConfig.shared.currentProductionURL
        print("🔗 [iOS] Testing production URL: \(url)")
        
        guard let testURL = URL(string: "\(url)/health") else {
            print("❌ [iOS] Invalid production URL")
            return false
        }
        
        do {
            let request = URLRequest(url: testURL, timeoutInterval: 5.0)
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                let isReachable = httpResponse.statusCode == 200
                print("📡 [iOS] Production URL - Status: \(httpResponse.statusCode), Reachable: \(isReachable)")
                return isReachable
            }
            
            return false
        } catch {
            print("❌ [iOS] Production URL test failed: \(error.localizedDescription)")
            return false
        }
    }
}

// MARK: - Usage Instructions

/*
 
 HOW TO UPDATE PRODUCTION URL:
 
 1. Deploy your backend to Vercel
 2. Get the Vercel URL (e.g., https://spendsmart-backend.vercel.app)
 3. Call this function in your app:
 
    ProductionURLUpdater.updateProductionURL("https://spendsmart-backend.vercel.app")
 
 4. Test the URL:
 
    let isReachable = await ProductionURLUpdater.testProductionURL()
    print("Production URL reachable: \(isReachable)")
 
 ENVIRONMENT BEHAVIOR:
 
 - DEBUG builds (Development):
   * Tests localhost first (requires local network permission)
   * Falls back to production URL if localhost unavailable
   * Shows local network permission popup
 
 - RELEASE builds (Production):
   * Always uses production URL
   * No local network permission popup
   * No localhost testing
 
 */ 