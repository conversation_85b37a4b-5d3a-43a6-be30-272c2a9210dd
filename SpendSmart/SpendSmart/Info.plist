<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIAppFonts</key>
	<array>
		<string>SpaceGrotesk-Variable.ttf</string>
		<string>InstrumentSerif-Regular.ttf</string>
		<string>InstrumentSans-Variable.ttf</string>
		<string>InstrumentSerif-Italic.ttf</string>
	</array>
	<key>UILaunchScreen</key>
	<dict/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>SpendSmart uses your location to show nearby stores and spending patterns on the map.</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<!-- Development-only exceptions for localhost -->
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>127.0.0.1</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
			</dict>
			<key>*************</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
			</dict>
			<!-- Production domain exceptions -->
			<key>your-vercel-app.vercel.app</key>
			<dict>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<!-- Local Network Usage Description (only shown in development) -->
	<key>NSLocalNetworkUsageDescription</key>
	<string>SpendSmart needs access to your local network to connect to the development server.</string>
</dict>
</plist>
